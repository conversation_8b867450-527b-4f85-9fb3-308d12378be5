# Matrix服务器部署快速参考指南

## 🚀 一键部署命令

```bash
# 1. 下载并运行部署脚本
curl -fsSL https://raw.githubusercontent.com/your-repo/matrix-deploy/main/setup.sh | bash

# 2. 或者手动克隆仓库
git clone https://github.com/your-repo/matrix-deploy.git
cd matrix-deploy
chmod +x setup.sh
./setup.sh
```

## 📋 关键配置变量

### 必需配置
```bash
# 域名配置
DOMAIN=example.com
MATRIX_SUBDOMAIN=matrix
ELEMENT_SUBDOMAIN=chat
RTC_SUBDOMAIN=rtc
MAS_SUBDOMAIN=mas
TURN_SUBDOMAIN=turn

# 服务器IP
EXTERNAL_SERVER_IP=*******           # 外部服务器公网IP
INTERNAL_SERVER_IP=*************     # 内部服务器内网IP

# RouterOS配置
ROUTEROS_IP=***********
ROUTEROS_USERNAME=matrix-api
ROUTEROS_PASSWORD=your-password
WAN_INTERFACE=internet

# Cloudflare配置
CLOUDFLARE_API_TOKEN=your-token-here

# 证书环境
CERT_ENVIRONMENT=staging             # staging 或 production
```

### 端口配置
```bash
# 外网端口（用户自定义）
HTTPS_PORT=8443
FEDERATION_PORT=8448
WEBRTC_TCP_PORT=30881
UDP_MUX_PORT=30882
TURN_UDP_PORT=3478
TURN_TLS_PORT=5349
UDP_PORT_RANGE_START=30152
UDP_PORT_RANGE_END=30352

# 内网端口（固定）
INTERNAL_HTTPS_PORT=30443
INTERNAL_FEDERATION_PORT=30448
INTERNAL_WEBRTC_TCP_PORT=30881
INTERNAL_UDP_MUX_PORT=30882
INTERNAL_TURN_UDP_PORT=30478
INTERNAL_TURN_TLS_PORT=30349
```

## 🔧 关键命令速查

### K3s和Kubectl
```bash
# 检查集群状态
kubectl get nodes
kubectl get pods -A

# 检查Matrix服务
kubectl get pods -n matrix
kubectl get services -n matrix
kubectl get certificates -n matrix

# 查看日志
kubectl logs -f <pod-name> -n matrix
kubectl describe pod <pod-name> -n matrix
```

### Helm操作
```bash
# 查看已安装的releases
helm list -n matrix

# 升级Matrix服务
helm upgrade matrix element-hq/element-server-suite \
    -n matrix -f matrix-values.yaml

# 回滚到上一版本
helm rollback matrix -n matrix
```

### 证书管理
```bash
# 检查证书状态
kubectl get certificates -n matrix
kubectl describe certificate matrix-tls -n matrix

# 强制重新申请证书
kubectl delete certificate matrix-tls -n matrix
kubectl apply -f cluster-issuer.yaml
```

### IP监控服务
```bash
# 检查IP监控服务状态
sudo systemctl status matrix-ip-monitor

# 查看IP监控日志
sudo journalctl -u matrix-ip-monitor -f

# 重启IP监控服务
sudo systemctl restart matrix-ip-monitor

# 手动测试IP监控
cd ~/matrix/routeros-scripts
source venv/bin/activate
python3 ip_monitor.py --test-once
```

## 🌐 DNS配置清单

### Cloudflare DNS记录
```
类型    名称                值                    说明
A       @                  EXTERNAL_SERVER_IP    主域名（外部服务器）
A       matrix             ROUTEROS_WAN_IP       Synapse API
A       chat               ROUTEROS_WAN_IP       Element Web
A       rtc                ROUTEROS_WAN_IP       Matrix RTC
A       mas                ROUTEROS_WAN_IP       Matrix Auth
A       turn               ROUTEROS_WAN_IP       TURN服务器
```

### RouterOS端口转发
```bash
# 在RouterOS终端执行
/ip firewall nat add chain=dstnat protocol=tcp dst-port=8443 action=dst-nat to-addresses=************* to-ports=30443
/ip firewall nat add chain=dstnat protocol=tcp dst-port=8448 action=dst-nat to-addresses=************* to-ports=30448
/ip firewall nat add chain=dstnat protocol=tcp dst-port=30881 action=dst-nat to-addresses=************* to-ports=30881
/ip firewall nat add chain=dstnat protocol=udp dst-port=30882 action=dst-nat to-addresses=************* to-ports=30882
/ip firewall nat add chain=dstnat protocol=udp dst-port=3478 action=dst-nat to-addresses=************* to-ports=30478
/ip firewall nat add chain=dstnat protocol=tcp dst-port=5349 action=dst-nat to-addresses=************* to-ports=30349
/ip firewall nat add chain=dstnat protocol=udp dst-port=30152-30352 action=dst-nat to-addresses=************* to-ports=30152-30352
```

## 🧪 测试命令

### 基础连接测试
```bash
# 测试well-known配置
curl https://example.com/.well-known/matrix/client
curl https://example.com/.well-known/matrix/server

# 测试Matrix API
curl https://matrix.example.com:8443/_matrix/client/versions

# 测试Element Web
curl -I https://chat.example.com:8443/

# 测试Federation
curl https://matrix.example.com:8448/_matrix/federation/v1/version
```

### TURN服务测试
```bash
# 获取当前WAN IP
CURRENT_WAN_IP=$(curl -s ifconfig.me)

# 测试TURN端口
nc -u -z $CURRENT_WAN_IP 3478    # TURN UDP
nc -z $CURRENT_WAN_IP 5349       # TURN TLS
```

### 内部服务测试
```bash
# 测试内部端口
curl -k https://*************:30443/_matrix/client/versions
curl -k https://*************:30448/_matrix/federation/v1/version
```

## 🔍 故障排除

### 常见问题快速诊断
```bash
# 运行完整诊断
~/matrix/diagnose.sh

# 检查Pod状态
kubectl get pods -n matrix | grep -v Running

# 检查最近事件
kubectl get events -n matrix --sort-by='.lastTimestamp' | tail -10

# 检查证书问题
kubectl describe certificate matrix-tls -n matrix

# 检查IP监控问题
sudo journalctl -u matrix-ip-monitor --since "1 hour ago"
```

### 服务重启命令
```bash
# 重启所有Matrix服务
kubectl rollout restart deployment -n matrix

# 重启特定服务
kubectl rollout restart deployment/synapse -n matrix
kubectl rollout restart deployment/coturn -n matrix
kubectl rollout restart deployment/element-web -n matrix

# 重启IP监控服务
sudo systemctl restart matrix-ip-monitor
```

### 证书问题解决
```bash
# 删除并重新申请证书
kubectl delete certificate matrix-tls -n matrix

# 检查cert-manager日志
kubectl logs -n cert-manager -l app.kubernetes.io/name=cert-manager

# 验证Cloudflare API Token
curl -X GET "https://api.cloudflare.com/client/v4/zones" \
     -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN"
```

## 📊 监控和维护

### 日常检查命令
```bash
# 检查系统资源
kubectl top nodes
kubectl top pods -n matrix

# 检查存储使用
df -h
kubectl get pv

# 检查服务状态
kubectl get all -n matrix

# 检查证书有效期
kubectl get certificates -n matrix -o wide
```

### 备份命令
```bash
# 手动备份
~/matrix/backup.sh

# 检查备份文件
ls -la /backup/matrix-*

# 恢复备份（示例）
tar -xzf /backup/matrix-20250103-120000.tar.gz
```

## 🔐 安全检查清单

### 防火墙验证
```bash
# 检查防火墙状态
sudo ufw status verbose

# 检查开放端口
ss -tlnp | grep -E ":(30443|30448|30881|30882|30478|30349)"
```

### 访问控制验证
```bash
# 检查Matrix注册设置
kubectl exec -n matrix $(kubectl get pods -n matrix -l app.kubernetes.io/name=synapse -o jsonpath='{.items[0].metadata.name}') -- \
    grep -A5 "enable_registration" /data/homeserver.yaml

# 检查管理员用户
kubectl exec -n matrix $(kubectl get pods -n matrix -l app.kubernetes.io/name=synapse -o jsonpath='{.items[0].metadata.name}') -- \
    sqlite3 /data/homeserver.db "SELECT name, admin FROM users WHERE admin=1;"
```

## 📞 紧急恢复

### 服务完全停止时
```bash
# 1. 检查K3s状态
sudo systemctl status k3s

# 2. 重启K3s（如果需要）
sudo systemctl restart k3s

# 3. 等待服务恢复
kubectl wait --for=condition=ready pod -l app.kubernetes.io/instance=matrix -n matrix --timeout=300s

# 4. 检查IP监控服务
sudo systemctl restart matrix-ip-monitor
```

### IP变化导致的服务中断
```bash
# 1. 手动触发IP更新
cd ~/matrix/routeros-scripts
source venv/bin/activate
python3 ip_monitor.py --force-update

# 2. 重启TURN服务
kubectl rollout restart deployment/coturn -n matrix

# 3. 等待服务恢复
kubectl rollout status deployment/coturn -n matrix --timeout=60s
```

## 📱 客户端配置

### Element Web访问
- URL: `https://chat.example.com:8443`
- 服务器: `matrix.example.com:8443`

### 移动客户端配置
- 服务器: `https://matrix.example.com:8443`
- 身份服务器: `https://vector.im`

### 桌面客户端配置
- 服务器: `matrix.example.com:8443`
- 端口: `8443`

---

**注意**: 将示例域名 `example.com` 和IP地址替换为您的实际配置。
