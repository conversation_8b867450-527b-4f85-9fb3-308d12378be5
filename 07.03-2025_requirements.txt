# Matrix服务器部署需求文档
# 基于element-hq/ess-helm 25.6.2版本的完整Matrix部署包

## 🏗️ 架构需求

### 网络架构
```
[外网用户] → [外部服务器nginx] → [RouterOS端口转发] → [内部服务器K3s]
```

### 内外网分离要求
- **外部服务器**: 仅运行nginx反向代理，处理SSL终止和域名路由，提供Matrix well-known配置
- **内部服务器**: 运行完整Matrix服务栈，位于内网环境
- **RouterOS**: 手动配置端口转发，提供动态IP检测API
- **DNS管理**: 主域名手动管理指向外部服务器，子域名通过RouterOS API自动更新

## 🔌 端口配置需求

### RouterOS端口转发（所有端口用户自定义）
```
外网端口 → 内部服务器端口
├── ${HTTPS_PORT} → ${INTERNAL_SERVER_IP}:${INTERNAL_HTTPS_PORT} (所有子域名HTTPS)
├── ${FEDERATION_PORT} → ${INTERNAL_SERVER_IP}:${INTERNAL_FEDERATION_PORT} (Federation)
├── ${WEBRTC_TCP_PORT} → ${INTERNAL_SERVER_IP}:${INTERNAL_WEBRTC_TCP_PORT} (WebRTC TCP)
├── ${UDP_MUX_PORT} → ${INTERNAL_SERVER_IP}:${INTERNAL_UDP_MUX_PORT} (UDP Mux)
├── ${TURN_UDP_PORT} → ${INTERNAL_SERVER_IP}:${INTERNAL_TURN_UDP_PORT} (TURN UDP)
├── ${TURN_TLS_PORT} → ${INTERNAL_SERVER_IP}:${INTERNAL_TURN_TLS_PORT} (TURN TLS)
└── ${UDP_PORT_RANGE_START}-${UDP_PORT_RANGE_END} → ${INTERNAL_SERVER_IP}:相同端口段 (UDP端口段)
```

### 默认端口建议
```
外网端口（用户自定义）:
- HTTPS_PORT=8443                   # 所有子域名HTTPS端口
- FEDERATION_PORT=8448              # Matrix Federation端口
- WEBRTC_TCP_PORT=30881             # WebRTC TCP连接端口
- UDP_MUX_PORT=30882                # WebRTC UDP多路复用端口
- TURN_UDP_PORT=3478                # TURN UDP端口
- TURN_TLS_PORT=5349                # TURN TLS端口
- UDP_PORT_RANGE_START=33152        # WebRTC UDP端口范围开始
- UDP_PORT_RANGE_END=33352          # WebRTC UDP端口范围结束

内部端口（固定，基于上游项目默认配置）:
- INTERNAL_HTTPS_PORT=30443         # NodePort固定端口
- INTERNAL_FEDERATION_PORT=30448    # NodePort固定端口
- INTERNAL_WEBRTC_TCP_PORT=30881    # NodePort固定端口
- INTERNAL_UDP_MUX_PORT=30882       # NodePort固定端口
- INTERNAL_TURN_UDP_PORT=30478      # NodePort固定端口
- INTERNAL_TURN_TLS_PORT=30349      # NodePort固定端口

注意：
- TURN服务创建独立Pod（coturn），3478和5349是标准TURN端口
- 内部端口基于上游项目默认配置，不需要用户自定义
- 外网端口完全由用户自定义，适应不同网络环境
```

## 🌐 域名配置需求

### 域名结构
```
主域名: ${DOMAIN}
子域名:
├── ${MATRIX_SUBDOMAIN}.${DOMAIN} (Synapse API)
├── ${ELEMENT_SUBDOMAIN}.${DOMAIN} (Element Web)
├── ${RTC_SUBDOMAIN}.${DOMAIN} (Matrix RTC SFU)
├── ${MAS_SUBDOMAIN}.${DOMAIN} (Matrix Auth Service)
└── ${TURN_SUBDOMAIN}.${DOMAIN} (TURN服务器，遵循官方ESS与coturn最佳实践)
```

### DNS记录配置
```
类型    名称                        值                    说明
A       @                          ${EXTERNAL_SERVER_IP} 主域名（外部服务器，处理well-known）
A       ${ELEMENT_SUBDOMAIN}       ${ROUTEROS_WAN_IP}    Element Web
A       ${MATRIX_SUBDOMAIN}        ${ROUTEROS_WAN_IP}    Synapse API
A       ${RTC_SUBDOMAIN}           ${ROUTEROS_WAN_IP}    Matrix RTC
A       ${MAS_SUBDOMAIN}           ${ROUTEROS_WAN_IP}    Matrix Auth
A       ${TURN_SUBDOMAIN}          ${ROUTEROS_WAN_IP}    TURN服务器

重要：
❌ 不要添加泛域名记录 (*)，避免DNS冲突和安全风险
✅ 只添加需要的具体子域名，确保精确控制
✅ 主域名指向外部服务器IP（用户手动配置，RouterOS API脚本不会修改）
✅ 子域名指向RouterOS WAN口公网IP（RouterOS API脚本自动检测和更新）
✅ TURN服务需要独立子域名，用于TLS证书验证和客户端连接
✅ DNS管理分离：主域名手动管理，子域名手动管理
```

### 外部nginx配置（仅处理well-known和重定向）
```
外部服务器功能（独立公网IP）：
├── https://${DOMAIN}/.well-known/matrix/client → 返回homeserver发现信息
├── https://${DOMAIN}/.well-known/matrix/server → 返回federation发现信息
└── https://${DOMAIN}/* → 重定向到 https://${ELEMENT_SUBDOMAIN}.${DOMAIN}:${HTTPS_PORT}

客户端直连内部服务器（通过RouterOS端口转发）：
├── https://${ELEMENT_SUBDOMAIN}.${DOMAIN}:${HTTPS_PORT} → RouterOS:${HTTPS_PORT} → 内部服务器:${INTERNAL_HTTPS_PORT}
├── https://${MATRIX_SUBDOMAIN}.${DOMAIN}:${HTTPS_PORT} → RouterOS:${HTTPS_PORT} → 内部服务器:${INTERNAL_HTTPS_PORT}
├── https://${RTC_SUBDOMAIN}.${DOMAIN}:${HTTPS_PORT} → RouterOS:${HTTPS_PORT} → 内部服务器:${INTERNAL_HTTPS_PORT}
├── https://${MAS_SUBDOMAIN}.${DOMAIN}:${HTTPS_PORT} → RouterOS:${HTTPS_PORT} → 内部服务器:${INTERNAL_HTTPS_PORT}
└── https://${MATRIX_SUBDOMAIN}.${DOMAIN}:${FEDERATION_PORT} → RouterOS:${FEDERATION_PORT} → 内部服务器:${INTERNAL_FEDERATION_PORT}

注意：
- TURN服务的external_ip必须配置为RouterOS WAN口的真实IP地址
- TURN服务使用独立端口，不通过HTTPS代理：
  ├── turn:${TURN_SUBDOMAIN}.${DOMAIN}:${TURN_UDP_PORT} (UDP)
  └── turns:${TURN_SUBDOMAIN}.${DOMAIN}:${TURN_TLS_PORT} (TLS)
- IP变化时只需重启LiveKit服务，TURN服务会跟着重启
- 重启流程：检测IP变化→更新配置→重启服务→健康检查→更新DNS
```

## 🔒 SSL证书需求

### 证书分离策略
- **外部服务器**: 只申请主域名证书 `${DOMAIN}` (仅处理主域名的well-known和重定向)
- **内部服务器**: 申请子域名证书 `${ELEMENT_SUBDOMAIN}.${DOMAIN}`, `${MATRIX_SUBDOMAIN}.${DOMAIN}`, `${RTC_SUBDOMAIN}.${DOMAIN}`, `${MAS_SUBDOMAIN}.${DOMAIN}`, `${TURN_SUBDOMAIN}.${DOMAIN}` (处理实际Matrix服务的SSL终止)

### DNS安全策略
- **精确配置**: 只添加实际需要的6个域名记录（1主域名+5子域名）
- **避免泛域名**: 不使用 `*` 泛域名记录，防止DNS冲突和安全风险
- **统一指向**: 所有子域名DNS记录都指向RouterOS WAN口公网IP，客户端通过well-known发现内部服务器

### 证书申请要求
- **外部服务器**: 使用certbot + HTTP验证直接申请主域名生产证书（有公网IP）
- **内部服务器**: 使用cert-manager + DNS验证申请子域名证书（K3s环境），提供生产/测试证书选项
- **邮箱条款**: 不同意暴露邮箱条款 (`--no-eff-email`)
- **邮箱地址**: 使用 `acme@${DOMAIN}` 格式

### 证书管理方式
```
外部服务器（certbot + HTTP验证）:
- 工具: certbot
- 域名: ${DOMAIN}
- 验证: HTTP-01 (端口80)
- 证书: 直接申请生产证书
- 优点: 简单，不需要API Token，有公网IP可直接验证
- 自动续期

内部服务器（cert-manager + DNS验证）:
- 工具: cert-manager (Kubernetes原生)
- 域名: ${MATRIX_SUBDOMAIN}.${DOMAIN}, ${ELEMENT_SUBDOMAIN}.${DOMAIN}, ${RTC_SUBDOMAIN}.${DOMAIN}, ${MAS_SUBDOMAIN}.${DOMAIN}, ${TURN_SUBDOMAIN}.${DOMAIN}
- 验证: DNS-01 (Cloudflare API Token)
- 证书环境: 用户可选择 staging（测试）或 production（生产）
- 优点: K3s集成，自动化管理，内网环境适用
- 自动续期
```

### 内部服务器证书环境配置
```
测试证书（推荐先测试）:
- CERT_ENVIRONMENT=staging
- 服务器: https://acme-staging-v02.api.letsencrypt.org/directory
- 优点: 无速率限制，适合测试和开发
- 缺点: 浏览器显示不受信任

生产证书（测试成功后）:
- CERT_ENVIRONMENT=production
- 服务器: https://acme-v02.api.letsencrypt.org/directory
- 优点: 浏览器信任，正式使用
- 缺点: 有速率限制（每周50个证书）

注意：外部服务器始终申请生产证书，只有内部服务器需要选择证书环境
```

## 🚀 内部服务器Matrix服务组件需求

### 必需组件
- ✅ **Synapse**: Matrix主服务器 (上游项目最新稳定版使用的版本)
- ✅ **Element Web**: Web客户端 (上游项目最新稳定版使用的版本)
- ✅ **PostgreSQL**: 数据库服务 (上游项目最新稳定版使用的版本)
- ✅ **Matrix RTC**: WebRTC服务 (必需！上游项目最新稳定版使用的版本)
- ✅ **Matrix Authentication Service**: 认证服务
- ✅ **HAProxy**: 负载均衡器

### TURN服务器需求
- ✅ **TURN**: 创建独立Pod（coturn）TURN服务器
- ❌ **禁用Google TURN**: 完全禁用外部STUN/TURN服务器
- ✅ **UDP端口段**: 支持动态UDP端口分配 (30152-30352)

### 禁用组件
- ❌ **Traefik**: 禁用Traefik Ingress控制器，使用纯NodePort
- ❌ **Well-known**: 内部服务器不处理well-known，由外部服务器处理
- ❌ **外部代理**: 外部服务器不代理Matrix流量，客户端直连内部服务器

## 🔧 RouterOS集成需求
Debian 12 python虚拟环境自动配置

### API配置
```
RouterOS IP: ${ROUTEROS_IP} （默认：***********）
用户名: ${ROUTEROS_USERNAME} （默认：matrix-api）
密码: ${ROUTEROS_PASSWORD} （默认：matrix-api）
WAN接口: ${WAN_INTERFACE} (默认: internet)
API端口: 8728 (传统API)
```

### 动态IP检测和DNS管理
- **获取方式**: 直接从RouterOS WAN口获取真实公网IP
- **更新频率**: 每10秒检查一次IP变化
- **重启优化**: IP变化时快速重启turn服务，优化配置生成和启动时间
- **配置更新**: 更新turn配置中的external_ip并重启服务
- **健康检查**: 等待turn服务完全就绪后再更新DNS
- **DNS更新策略**:
  - ✅ 只更新子域名DNS记录 (matrix, chat, rtc, mas, turn)
  - ❌ 不修改主域名DNS记录 (主域名手动管理)
  - ✅ 使用Cloudflare API自动更新子域名指向新IP
- **中断时间**: 预期60秒内恢复，客户端自动重连

### 快速重启策略（最终方案）
- **重启方式**: IP变化时立即重启turn服务，目标60秒内恢复
- **中断现实**: 正在进行的视频会议会短暂中断，需要重新加入
- **恢复优化**: 优化配置生成、服务启动和健康检查速度
- **用户体验**: Matrix客户端自动重连，文字聊天不受影响
- **预防措施**: 监控IP变化模式，提供维护通知
- **适用场景**: 单服务器环境，非关键业务，可接受短暂中断

### 端口转发
- **配置方式**: 手动配置，不需要自动配置
- **转发规则**: 所有外网端口转发到内部服务器对应端口

## 📦 部署包需求

### 一键部署支持
```bash
# 支持curl一键部署
bash <(curl -fsSL URL/setup.sh)
```

### 交互式配置
- 域名配置 (主域名和子域名)
- 端口配置 (所有外网端口，显示对应的服务)
- 目录配置 (安装目录，默认: ~/matrix/)
- RouterOS配置 (IP、用户名、密码、WAN接口)
- Cloudflare配置 (API Token)

### 配置文件格式
- 使用 `${VARIABLE_NAME}` 格式的变量占位符
- 支持完全自定义的所有参数
- 保持中文注释风格
- 每个文件包含详细使用说明

## 🧪 测试需求

### 功能测试
1. **基础连接测试**: HTTP/HTTPS端口访问
2. **Matrix客户端测试**: Element Web功能
3. **Federation测试**: 与其他Matrix服务器联邦
4. **WebRTC测试**: 音视频通话功能
5. **TURN测试**: NAT穿透功能

### 架构验证
1. **内外网分离**: 验证外部nginx重定向和well-known指向内部服务子域名+自定义端口
2. **RouterOS转发**: 验证端口转发配置
3. **SSL证书**: 验证证书申请和配置
4. **动态IP**: 验证RouterOS IP检测

## 🛠️ 技术栈需求

### 内部服务器环境
- **操作系统**: Debian
- **容器运行时**: Docker
- **Kubernetes**: K3s
- **包管理器**: Helm
- **Python**: Python3 (RouterOS API)/注意Debian 12 不支持直接运行python，需要创建虚拟环境

### 外部服务器环境
- **Web服务器**: nginx (仅处理well-known和重定向，无Matrix流量代理)
- **SSL工具**: Certbot + Cloudflare DNS插件
- **最低配置**: 512MB内存，1核CPU (轻量级配置)

## 📋 配置变量清单

### 必需变量
```bash
# 域名配置
DOMAIN=
MATRIX_SUBDOMAIN=matrix
ELEMENT_SUBDOMAIN=chat
RTC_SUBDOMAIN=rtc
MAS_SUBDOMAIN=mas
TURN_SUBDOMAIN=turn

# 外网端口配置
HTTPS_PORT=8443
FEDERATION_PORT=8448
WEBRTC_TCP_PORT=30881
UDP_MUX_PORT=30882
TURN_UDP_PORT=3478
TURN_TLS_PORT=5349
UDP_PORT_RANGE_START=30152
UDP_PORT_RANGE_END=30352

# 内网端口配置（固定，基于上游项目默认配置）
INTERNAL_HTTPS_PORT=30443
INTERNAL_FEDERATION_PORT=30448
INTERNAL_WEBRTC_TCP_PORT=30881
INTERNAL_UDP_MUX_PORT=30882
INTERNAL_TURN_UDP_PORT=30478
INTERNAL_TURN_TLS_PORT=30349

# RouterOS配置
ROUTEROS_IP=
ROUTEROS_USERNAME=
ROUTEROS_PASSWORD=
WAN_INTERFACE=

# RouterOS动态IP配置
ROUTEROS_WAN_IP=                       # 自动检测：RouterOS WAN口真实公网IP
DNS_UPDATE_INTERVAL=10                 # DNS检查间隔（秒）

# 说明：
# - ROUTEROS_WAN_IP: 通过RouterOS API自动获取，用于子域名DNS记录和LiveKit配置

# Cloudflare配置
CLOUDFLARE_API_TOKEN=

# 证书配置用户自定义
CERT_ENVIRONMENT=staging  # staging（测试）或 production（生产）

# 安装配置用户自定义
INSTALL_DIR=~/matrix/
```

## ✅ 成功标准

### 部署成功标准
1. 所有Matrix服务Pod运行正常
2. 所有NodePort端口正确映射
3. SSL证书申请成功
4. 外部nginx反向代理配置正确
5. RouterOS端口转发生效
6. 交互式用户管理

### 功能成功标准
1. 用户可通过 https://${ELEMENT_SUBDOMAIN}.${DOMAIN}:${HTTPS_PORT} 访问Element Web
2. Matrix客户端可正常注册和登录（需要设置邀请注册开关）
3. 音视频通话功能正常
4. Federation功能正常
5. 动态IP检测和快速重启正常（60秒内恢复）
6. 用户了解IP变化时视频会议会短暂中断的现实

---

**注意**: 内部服务器的所有对外服务端口支持用户自定义，外部服务器只需要标准的80/443端口。
