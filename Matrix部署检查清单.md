# Matrix服务器部署检查清单

## 📋 部署前准备检查

### 硬件和网络要求 ✅

**外部服务器（VPS）:**
- [ ] CPU: 1核心或以上
- [ ] 内存: 512MB或以上  
- [ ] 存储: 10GB或以上
- [ ] 网络: 独立公网IP
- [ ] 系统: Debian 11/12 或 Ubuntu 20.04/22.04

**内部服务器:**
- [ ] CPU: 4核心（推荐）
- [ ] 内存: 8GB（最低4GB）
- [ ] 存储: 100GB SSD
- [ ] 网络: 千兆网卡，固定内网IP
- [ ] 系统: Debian 12

**RouterOS设备:**
- [ ] 支持API功能的MikroTik设备
- [ ] 固件版本: 7.x
- [ ] WAN口已配置获取公网IP
- [ ] 管理员访问权限

### 域名和DNS准备 ✅

**域名要求:**
- [ ] 拥有一个可管理的域名（如 example.com）
- [ ] 域名DNS托管在Cloudflare
- [ ] 已获取Cloudflare API Token（Zone:Edit权限）

**DNS记录规划:**
- [ ] 主域名: `example.com` → 外部服务器IP
- [ ] Matrix子域名: `matrix.example.com` → RouterOS WAN IP
- [ ] Element子域名: `chat.example.com` → RouterOS WAN IP  
- [ ] RTC子域名: `rtc.example.com` → RouterOS WAN IP
- [ ] Auth子域名: `mas.example.com` → RouterOS WAN IP
- [ ] TURN子域名: `turn.example.com` → RouterOS WAN IP

### 账号和凭据准备 ✅

**Cloudflare:**
- [ ] Cloudflare账号
- [ ] API Token（Zone:Edit权限）
- [ ] 域名已添加到Cloudflare

**RouterOS:**
- [ ] 管理员账号密码
- [ ] API用户账号（将创建）
- [ ] WAN接口名称（通常是 "internet"）

**邮箱:**
- [ ] 用于SSL证书申请的邮箱地址

## 🔧 第一阶段：环境准备检查

### 配置变量设置 ✅
- [ ] 创建 `~/matrix-deploy/config.env` 文件
- [ ] 设置所有必需的域名变量
- [ ] 设置所有端口配置变量
- [ ] 设置服务器IP地址
- [ ] 设置RouterOS连接信息
- [ ] 设置Cloudflare API Token
- [ ] 设置证书环境（staging/production）
- [ ] 验证配置变量加载正确

### 目录结构创建 ✅
- [ ] 创建 `$INSTALL_DIR` 主目录
- [ ] 创建 `external-server` 子目录
- [ ] 创建 `routeros-scripts` 子目录
- [ ] 创建 `k3s-configs` 子目录
- [ ] 创建 `ssl-certs` 子目录
- [ ] 创建 `logs` 子目录

## 🌐 第二阶段：外部服务器配置检查

### 系统环境准备 ✅
- [ ] 系统更新完成 (`apt update && apt upgrade`)
- [ ] 安装nginx
- [ ] 安装certbot
- [ ] 安装python3-certbot-nginx
- [ ] nginx服务启动并启用
- [ ] nginx状态检查正常

### nginx基础配置 ✅
- [ ] 备份默认nginx配置
- [ ] 创建主域名虚拟主机配置
- [ ] 启用新站点配置
- [ ] 禁用默认站点
- [ ] nginx配置语法检查通过
- [ ] nginx服务重载成功

### SSL证书申请 ✅
- [ ] certbot申请主域名证书成功
- [ ] 证书文件存在于 `/etc/letsencrypt/live/`
- [ ] 证书验证命令执行成功
- [ ] 自动续期配置正常

### Matrix well-known配置 ✅
- [ ] 创建 `.well-known/matrix` 目录
- [ ] 配置客户端发现文件 (`client`)
- [ ] 配置服务器发现文件 (`server`)
- [ ] nginx well-known配置添加
- [ ] Content-Type头设置正确
- [ ] CORS头设置正确

### 完整nginx配置 ✅
- [ ] HTTP到HTTPS重定向配置
- [ ] SSL证书路径配置正确
- [ ] well-known路径配置正确
- [ ] 主域名到Element Web重定向配置
- [ ] nginx配置测试通过
- [ ] nginx服务重载成功

## 🔌 第三阶段：RouterOS配置检查

### API用户配置 ✅
- [ ] 创建matrix-api用户组
- [ ] 设置正确的API权限策略
- [ ] 创建API用户账号
- [ ] 设置用户密码
- [ ] 启用API服务
- [ ] 验证API服务状态

### 端口转发配置 ✅
- [ ] HTTPS端口转发 (8443 → 30443)
- [ ] Federation端口转发 (8448 → 30448)
- [ ] WebRTC TCP端口转发 (30881 → 30881)
- [ ] UDP Mux端口转发 (30882 → 30882)
- [ ] TURN UDP端口转发 (3478 → 30478)
- [ ] TURN TLS端口转发 (5349 → 30349)
- [ ] UDP端口段转发 (30152-30352)
- [ ] 验证所有端口转发规则

### 动态IP检测脚本 ✅
- [ ] 创建Python虚拟环境
- [ ] 安装routeros-api库
- [ ] 安装requests库
- [ ] 创建ip_monitor.py脚本
- [ ] 设置脚本执行权限
- [ ] 测试RouterOS API连接
- [ ] 测试WAN IP获取功能
- [ ] 测试Cloudflare API连接

## 🚀 第四阶段：内部服务器K3s部署检查

### 系统环境准备 ✅
- [ ] 系统更新完成
- [ ] 安装curl、wget、git
- [ ] 安装Python3和pip
- [ ] 安装Docker
- [ ] 用户添加到docker组
- [ ] Docker服务测试正常

### K3s安装配置 ✅
- [ ] K3s安装完成
- [ ] 禁用Traefik Ingress
- [ ] 禁用ServiceLB
- [ ] 设置节点外部IP
- [ ] 配置kubectl访问权限
- [ ] 验证K3s节点状态
- [ ] 验证系统Pod运行状态

### Helm安装配置 ✅
- [ ] Helm 3安装完成
- [ ] 验证Helm版本
- [ ] 添加jetstack仓库
- [ ] 添加element-hq仓库
- [ ] 更新Helm仓库

### cert-manager部署 ✅
- [ ] 创建cert-manager命名空间
- [ ] 安装cert-manager CRDs
- [ ] 部署cert-manager Helm chart
- [ ] 等待cert-manager Pod就绪
- [ ] 验证cert-manager安装

### Cloudflare DNS验证配置 ✅
- [ ] 创建Cloudflare API Token Secret
- [ ] 创建ClusterIssuer配置文件
- [ ] 应用ClusterIssuer配置
- [ ] 验证ClusterIssuer状态
- [ ] 检查ClusterIssuer详细信息

## 🔐 第五阶段：Matrix服务部署检查

### Matrix命名空间准备 ✅
- [ ] 创建matrix命名空间
- [ ] 设置默认命名空间上下文

### Matrix Helm Values配置 ✅
- [ ] 创建matrix-values.yaml文件
- [ ] 配置全局域名设置
- [ ] 配置PostgreSQL数据库
- [ ] 配置Synapse服务
- [ ] 配置Element Web
- [ ] 配置Matrix RTC
- [ ] 配置Matrix Authentication Service
- [ ] 配置HAProxy负载均衡
- [ ] 配置coturn TURN服务器
- [ ] 配置SSL证书设置
- [ ] 禁用不需要的组件

### Matrix服务部署 ✅
- [ ] 添加element-hq Helm仓库
- [ ] 更新Helm仓库
- [ ] 部署Matrix Helm chart
- [ ] 等待所有Pod就绪
- [ ] 检查Pod运行状态
- [ ] 检查Service配置
- [ ] 检查Ingress配置

### SSL证书验证 ✅
- [ ] 检查证书申请状态
- [ ] 查看证书详细信息
- [ ] 检查cert-manager日志
- [ ] 等待证书就绪状态
- [ ] 验证证书有效性

## 🧪 第六阶段：测试和验证检查

### 基础连接测试 ✅
- [ ] 测试Synapse API内部端口
- [ ] 测试Federation内部端口
- [ ] 测试Element Web内部端口
- [ ] 内部服务响应正常

### 外部访问测试 ✅
- [ ] 测试Matrix well-known客户端发现
- [ ] 测试Matrix well-known服务器发现
- [ ] 测试Element Web外部访问
- [ ] 测试Matrix API外部访问
- [ ] 外部服务响应正常

### WebRTC和TURN测试 ✅
- [ ] 检查coturn Pod状态
- [ ] 获取当前WAN IP地址
- [ ] 测试TURN UDP端口连通性
- [ ] 测试TURN TLS端口连通性
- [ ] TURN服务功能正常

### 管理员用户创建 ✅
- [ ] 进入Synapse Pod
- [ ] 创建管理员用户
- [ ] 记录用户名和密码
- [ ] 验证用户创建成功

## 🔧 第七阶段：动态IP监控部署检查

### IP监控服务部署 ✅
- [ ] 激活Python虚拟环境
- [ ] 创建systemd服务文件
- [ ] 配置服务环境变量
- [ ] 启用systemd服务
- [ ] 启动IP监控服务
- [ ] 检查服务运行状态
- [ ] 查看服务日志输出

### IP更新测试 ✅
- [ ] 手动运行IP监控脚本
- [ ] 测试RouterOS API连接
- [ ] 测试WAN IP获取
- [ ] 测试Cloudflare DNS更新
- [ ] 测试TURN服务重启
- [ ] 验证IP更新流程

## 🛡️ 第八阶段：安全配置检查

### 防火墙配置 ✅
- [ ] 安装ufw防火墙
- [ ] 设置默认拒绝策略
- [ ] 允许SSH访问
- [ ] 允许Matrix服务端口
- [ ] 允许TURN服务端口
- [ ] 允许UDP端口范围
- [ ] 启用防火墙
- [ ] 验证防火墙规则

### 系统优化 ✅
- [ ] 优化网络参数
- [ ] 应用sysctl配置
- [ ] 优化文件描述符限制
- [ ] 验证系统参数生效

### 备份配置 ✅
- [ ] 创建备份脚本
- [ ] 设置脚本执行权限
- [ ] 配置定期备份任务
- [ ] 测试备份脚本执行

## 🔍 第九阶段：故障排除检查

### 诊断工具准备 ✅
- [ ] 创建诊断脚本
- [ ] 设置脚本执行权限
- [ ] 测试诊断脚本运行
- [ ] 验证诊断信息完整

### 常见问题解决方案 ✅
- [ ] Pod启动问题排查方法
- [ ] 证书申请失败解决方案
- [ ] WebRTC通话问题排查
- [ ] Federation连接问题排查
- [ ] 网络连通性问题排查

## ✅ 第十阶段：最终验证检查

### 功能验证清单 ✅
- [ ] 创建验证脚本
- [ ] 设置脚本执行权限
- [ ] 运行完整功能验证
- [ ] 所有验证项目通过

### 成功标准确认 ✅

**部署成功标准:**
- [ ] 所有Matrix服务Pod运行正常
- [ ] 所有NodePort端口正确映射
- [ ] SSL证书申请成功
- [ ] 外部nginx反向代理配置正确
- [ ] RouterOS端口转发生效
- [ ] 动态IP检测和快速重启正常

**功能成功标准:**
- [ ] 用户可访问Element Web界面
- [ ] Matrix客户端可正常注册和登录
- [ ] 音视频通话功能正常
- [ ] Federation功能正常
- [ ] IP变化时服务能在60秒内恢复

### 最终测试 ✅
- [ ] 使用Element Web客户端登录
- [ ] 创建测试房间
- [ ] 发送文本消息
- [ ] 发送文件附件
- [ ] 进行音视频通话测试
- [ ] 测试屏幕共享功能
- [ ] 邀请外部用户测试Federation

## 📚 部署后维护检查

### 日常监控 ✅
- [ ] 设置系统资源监控
- [ ] 设置服务状态监控
- [ ] 设置证书到期监控
- [ ] 设置备份状态监控

### 定期维护 ✅
- [ ] 系统更新计划
- [ ] 证书续期检查
- [ ] 备份恢复测试
- [ ] 性能优化评估

---

**部署完成确认:**

- [ ] 所有检查项目已完成
- [ ] 所有测试验证通过
- [ ] 用户文档已交付
- [ ] 维护计划已制定

**签名确认:**
- 部署工程师: _________________ 日期: _________
- 项目负责人: _________________ 日期: _________
