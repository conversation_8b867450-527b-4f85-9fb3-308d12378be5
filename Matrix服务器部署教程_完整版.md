# Matrix服务器完整部署教程

## 📋 教程概述

本教程基于 `07.03-2025_requirements.txt` 需求文档，指导您完成Matrix服务器的完整部署。教程面向有一定Linux基础但对Matrix/K3s不熟悉的技术用户。

### 🎯 部署目标

完成一个具有以下特性的Matrix服务器：
- **内外网分离架构**：外部服务器处理域名发现，内部服务器运行Matrix服务
- **动态IP支持**：通过RouterOS API自动处理公网IP变化
- **完整WebRTC功能**：包含独立TURN服务器，支持音视频通话
- **SSL证书自动管理**：外部服务器使用certbot，内部服务器使用cert-manager
- **高度可定制**：所有端口和域名完全可配置

### 🏗️ 架构概览

```
[外网用户] → [外部服务器nginx] → [RouterOS端口转发] → [内部服务器K3s]
```

**组件说明：**
- **外部服务器**：独立公网IP，运行nginx，处理Matrix well-known发现和重定向
- **RouterOS**：家用路由器，提供端口转发和动态IP检测API
- **内部服务器**：运行K3s集群，部署完整Matrix服务栈

## 📦 环境要求

### 硬件要求

**外部服务器（VPS）：**
- CPU: 1核心
- 内存: 512MB
- 存储: 10GB
- 网络: 独立公网IP
- 系统: Debian 11/12 或 Ubuntu 20.04/22.04

**内部服务器：**
- CPU: 4核心（推荐）
- 内存: 8GB（最低4GB）
- 存储: 100GB SSD
- 网络: 千兆网卡
- 系统: Debian 12

**RouterOS设备：**
- 支持API功能的MikroTik设备
- 固件版本: 7.x
- 配置: 已设置WAN口获取公网IP

### 软件依赖

**外部服务器：**
- nginx
- certbot
- python3-certbot-nginx

**内部服务器：**
- Docker
- K3s
- Helm
- Python3（用于RouterOS API）

## 🔧 第一阶段：环境准备和变量配置

### 1.1 配置变量清单

创建配置文件，所有后续步骤都将使用这些变量：

```bash
# 在内部服务器创建配置文件
mkdir -p ~/matrix-deploy
cd ~/matrix-deploy
cat > config.env << 'EOF'
# ===========================================
# Matrix服务器部署配置文件
# 基于 07.03-2025_requirements.txt
# ===========================================

# 域名配置
DOMAIN=example.com                    # 替换为您的主域名
MATRIX_SUBDOMAIN=matrix
ELEMENT_SUBDOMAIN=chat
RTC_SUBDOMAIN=rtc
MAS_SUBDOMAIN=mas
TURN_SUBDOMAIN=turn

# 外网端口配置（用户自定义）
HTTPS_PORT=8443
FEDERATION_PORT=8448
WEBRTC_TCP_PORT=30881
UDP_MUX_PORT=30882
TURN_UDP_PORT=3478
TURN_TLS_PORT=5349
UDP_PORT_RANGE_START=30152
UDP_PORT_RANGE_END=30352

# 内网端口配置（固定，基于上游项目默认配置）
INTERNAL_HTTPS_PORT=30443
INTERNAL_FEDERATION_PORT=30448
INTERNAL_WEBRTC_TCP_PORT=30881
INTERNAL_UDP_MUX_PORT=30882
INTERNAL_TURN_UDP_PORT=30478
INTERNAL_TURN_TLS_PORT=30349

# 服务器IP配置
EXTERNAL_SERVER_IP=*******           # 替换为外部服务器公网IP
INTERNAL_SERVER_IP=*************     # 替换为内部服务器内网IP

# RouterOS配置
ROUTEROS_IP=***********               # RouterOS管理IP
ROUTEROS_USERNAME=matrix-api          # RouterOS API用户名
ROUTEROS_PASSWORD=matrix-api-password # RouterOS API密码
WAN_INTERFACE=internet                # WAN接口名称

# Cloudflare配置
CLOUDFLARE_API_TOKEN=your_cloudflare_api_token_here

# 证书配置
CERT_ENVIRONMENT=staging              # staging（测试）或 production（生产）

# 安装配置
INSTALL_DIR=~/matrix/

# 邮箱配置
ACME_EMAIL=acme@${DOMAIN}
EOF
```

### 1.2 加载配置变量

```bash
# 加载配置变量
source ~/matrix-deploy/config.env

# 验证关键变量
echo "主域名: $DOMAIN"
echo "外部服务器IP: $EXTERNAL_SERVER_IP"
echo "内部服务器IP: $INTERNAL_SERVER_IP"
echo "RouterOS IP: $ROUTEROS_IP"
```

### 1.3 创建目录结构

```bash
# 创建安装目录
mkdir -p $INSTALL_DIR
cd $INSTALL_DIR

# 创建子目录
mkdir -p {external-server,routeros-scripts,k3s-configs,ssl-certs,logs}

echo "目录结构创建完成："
tree $INSTALL_DIR
```

## 🌐 第二阶段：外部服务器配置

### 2.1 外部服务器环境准备

在外部服务器上执行以下步骤：

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必需软件
sudo apt install -y nginx certbot python3-certbot-nginx curl wget

# 启动并启用nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# 检查nginx状态
sudo systemctl status nginx
```

### 2.2 nginx基础配置

```bash
# 备份默认配置
sudo cp /etc/nginx/sites-available/default /etc/nginx/sites-available/default.backup

# 创建主域名配置
sudo tee /etc/nginx/sites-available/$DOMAIN << EOF
server {
    listen 80;
    server_name $DOMAIN;
    
    # Let's Encrypt验证路径
    location /.well-known/acme-challenge/ {
        root /var/www/html;
    }
    
    # 临时重定向到HTTPS（证书申请后启用）
    location / {
        return 301 https://\$server_name\$request_uri;
    }
}
EOF

# 启用站点
sudo ln -sf /etc/nginx/sites-available/$DOMAIN /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default

# 测试nginx配置
sudo nginx -t

# 重载nginx
sudo systemctl reload nginx
```

### 2.3 申请SSL证书

```bash
# 申请主域名证书（HTTP验证）
sudo certbot --nginx \
    --email $ACME_EMAIL \
    --agree-tos \
    --no-eff-email \
    --domains $DOMAIN

# 验证证书申请成功
sudo certbot certificates
```

### 2.4 配置Matrix well-known服务

```bash
# 创建well-known目录
sudo mkdir -p /var/www/html/.well-known/matrix

# 创建客户端发现配置
sudo tee /var/www/html/.well-known/matrix/client << EOF
{
    "m.homeserver": {
        "base_url": "https://$MATRIX_SUBDOMAIN.$DOMAIN:$HTTPS_PORT"
    },
    "m.identity_server": {
        "base_url": "https://vector.im"
    }
}
EOF

# 创建服务器发现配置
sudo tee /var/www/html/.well-known/matrix/server << EOF
{
    "m.server": "$MATRIX_SUBDOMAIN.$DOMAIN:$FEDERATION_PORT"
}
EOF

# 设置正确的Content-Type
sudo tee /etc/nginx/conf.d/matrix-well-known.conf << EOF
location /.well-known/matrix/ {
    root /var/www/html;
    add_header Content-Type application/json;
    add_header Access-Control-Allow-Origin *;
}
EOF
```

### 2.5 完整nginx配置

```bash
# 更新完整的nginx配置
sudo tee /etc/nginx/sites-available/$DOMAIN << EOF
server {
    listen 80;
    server_name $DOMAIN;
    
    # Let's Encrypt验证路径
    location /.well-known/acme-challenge/ {
        root /var/www/html;
    }
    
    # 重定向到HTTPS
    location / {
        return 301 https://\$server_name\$request_uri;
    }
}

server {
    listen 443 ssl http2;
    server_name $DOMAIN;
    
    # SSL证书配置
    ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;
    
    # Matrix well-known配置
    location /.well-known/matrix/ {
        root /var/www/html;
        add_header Content-Type application/json;
        add_header Access-Control-Allow-Origin *;
    }
    
    # 重定向到Element Web
    location / {
        return 301 https://$ELEMENT_SUBDOMAIN.$DOMAIN:$HTTPS_PORT;
    }
}
EOF

# 测试并重载nginx
sudo nginx -t && sudo systemctl reload nginx
```

## 🔌 第三阶段：RouterOS配置

### 3.1 RouterOS API用户配置

在RouterOS设备上创建API用户：

```bash
# 通过SSH或Winbox连接到RouterOS，执行以下命令：

# 创建API用户组
/user group add name=matrix-api policy=api,read,write

# 创建API用户
/user add name=$ROUTEROS_USERNAME password=$ROUTEROS_PASSWORD group=matrix-api

# 启用API服务
/ip service enable api

# 验证API服务状态
/ip service print where name=api
```

### 3.2 端口转发配置

```bash
# 在RouterOS上配置端口转发规则
# 注意：以下命令需要在RouterOS终端执行

# HTTPS端口转发
/ip firewall nat add chain=dstnat protocol=tcp dst-port=$HTTPS_PORT action=dst-nat to-addresses=$INTERNAL_SERVER_IP to-ports=$INTERNAL_HTTPS_PORT

# Federation端口转发
/ip firewall nat add chain=dstnat protocol=tcp dst-port=$FEDERATION_PORT action=dst-nat to-addresses=$INTERNAL_SERVER_IP to-ports=$INTERNAL_FEDERATION_PORT

# WebRTC TCP端口转发
/ip firewall nat add chain=dstnat protocol=tcp dst-port=$WEBRTC_TCP_PORT action=dst-nat to-addresses=$INTERNAL_SERVER_IP to-ports=$INTERNAL_WEBRTC_TCP_PORT

# UDP Mux端口转发
/ip firewall nat add chain=dstnat protocol=udp dst-port=$UDP_MUX_PORT action=dst-nat to-addresses=$INTERNAL_SERVER_IP to-ports=$INTERNAL_UDP_MUX_PORT

# TURN UDP端口转发
/ip firewall nat add chain=dstnat protocol=udp dst-port=$TURN_UDP_PORT action=dst-nat to-addresses=$INTERNAL_SERVER_IP to-ports=$INTERNAL_TURN_UDP_PORT

# TURN TLS端口转发
/ip firewall nat add chain=dstnat protocol=tcp dst-port=$TURN_TLS_PORT action=dst-nat to-addresses=$INTERNAL_SERVER_IP to-ports=$INTERNAL_TURN_TLS_PORT

# UDP端口段转发（WebRTC媒体流）
/ip firewall nat add chain=dstnat protocol=udp dst-port=$UDP_PORT_RANGE_START-$UDP_PORT_RANGE_END action=dst-nat to-addresses=$INTERNAL_SERVER_IP to-ports=$UDP_PORT_RANGE_START-$UDP_PORT_RANGE_END

# 验证端口转发规则
/ip firewall nat print where chain=dstnat
```

### 3.3 动态IP检测脚本

在内部服务器创建RouterOS API脚本：

```bash
# 创建Python虚拟环境（Debian 12要求）
cd $INSTALL_DIR/routeros-scripts
python3 -m venv venv
source venv/bin/activate

# 安装RouterOS API库
pip install routeros-api requests

# 创建动态IP检测脚本
cat > ip_monitor.py << 'EOF'
#!/usr/bin/env python3
"""
RouterOS动态IP检测和DNS更新脚本
基于 07.03-2025_requirements.txt 需求
"""

import time
import json
import requests
import subprocess
import logging
from routeros_api import RouterOsApi

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/tmp/ip_monitor.log'),
        logging.StreamHandler()
    ]
)

class IPMonitor:
    def __init__(self, config):
        self.config = config
        self.current_ip = None
        self.api = None
        
    def connect_routeros(self):
        """连接RouterOS API"""
        try:
            self.api = RouterOsApi(
                self.config['ROUTEROS_IP'],
                username=self.config['ROUTEROS_USERNAME'],
                password=self.config['ROUTEROS_PASSWORD']
            )
            logging.info("RouterOS API连接成功")
            return True
        except Exception as e:
            logging.error(f"RouterOS API连接失败: {e}")
            return False
    
    def get_wan_ip(self):
        """从RouterOS获取WAN口IP"""
        try:
            interfaces = self.api.get_resource('/interface')
            wan_interface = interfaces.get(name=self.config['WAN_INTERFACE'])
            if wan_interface:
                addresses = self.api.get_resource('/ip/address')
                for addr in addresses.get():
                    if addr['interface'] == self.config['WAN_INTERFACE']:
                        ip = addr['address'].split('/')[0]
                        return ip
            return None
        except Exception as e:
            logging.error(f"获取WAN IP失败: {e}")
            return None
    
    def update_dns_records(self, new_ip):
        """更新Cloudflare DNS记录"""
        headers = {
            'Authorization': f"Bearer {self.config['CLOUDFLARE_API_TOKEN']}",
            'Content-Type': 'application/json'
        }
        
        # 需要更新的子域名列表
        subdomains = [
            self.config['MATRIX_SUBDOMAIN'],
            self.config['ELEMENT_SUBDOMAIN'],
            self.config['RTC_SUBDOMAIN'],
            self.config['MAS_SUBDOMAIN'],
            self.config['TURN_SUBDOMAIN']
        ]
        
        for subdomain in subdomains:
            try:
                # 获取zone ID
                zone_response = requests.get(
                    f"https://api.cloudflare.com/client/v4/zones?name={self.config['DOMAIN']}",
                    headers=headers
                )
                zone_data = zone_response.json()
                zone_id = zone_data['result'][0]['id']
                
                # 获取DNS记录ID
                record_name = f"{subdomain}.{self.config['DOMAIN']}"
                records_response = requests.get(
                    f"https://api.cloudflare.com/client/v4/zones/{zone_id}/dns_records?name={record_name}",
                    headers=headers
                )
                records_data = records_response.json()
                
                if records_data['result']:
                    # 更新现有记录
                    record_id = records_data['result'][0]['id']
                    update_data = {
                        'type': 'A',
                        'name': record_name,
                        'content': new_ip,
                        'ttl': 300
                    }
                    
                    update_response = requests.put(
                        f"https://api.cloudflare.com/client/v4/zones/{zone_id}/dns_records/{record_id}",
                        headers=headers,
                        json=update_data
                    )
                    
                    if update_response.json()['success']:
                        logging.info(f"DNS记录更新成功: {record_name} -> {new_ip}")
                    else:
                        logging.error(f"DNS记录更新失败: {record_name}")
                
            except Exception as e:
                logging.error(f"更新DNS记录失败 {subdomain}: {e}")
    
    def restart_turn_service(self):
        """重启TURN服务"""
        try:
            # 更新TURN配置中的external_ip
            subprocess.run([
                'kubectl', 'patch', 'configmap', 'coturn-config',
                '-n', 'matrix',
                '--patch', f'{{"data":{{"external-ip":"{self.current_ip}"}}}}'
            ], check=True)
            
            # 重启TURN服务
            subprocess.run([
                'kubectl', 'rollout', 'restart', 'deployment/coturn',
                '-n', 'matrix'
            ], check=True)
            
            # 等待服务就绪
            subprocess.run([
                'kubectl', 'rollout', 'status', 'deployment/coturn',
                '-n', 'matrix', '--timeout=60s'
            ], check=True)
            
            logging.info("TURN服务重启成功")
            return True
        except Exception as e:
            logging.error(f"TURN服务重启失败: {e}")
            return False
    
    def monitor_loop(self):
        """主监控循环"""
        if not self.connect_routeros():
            return
        
        logging.info("开始IP监控...")
        
        while True:
            try:
                new_ip = self.get_wan_ip()
                
                if new_ip and new_ip != self.current_ip:
                    logging.info(f"检测到IP变化: {self.current_ip} -> {new_ip}")
                    
                    # 更新TURN服务配置
                    if self.restart_turn_service():
                        # 更新DNS记录
                        self.update_dns_records(new_ip)
                        self.current_ip = new_ip
                        logging.info("IP更新完成")
                    else:
                        logging.error("TURN服务重启失败，跳过DNS更新")
                
                time.sleep(int(self.config.get('DNS_UPDATE_INTERVAL', 10)))
                
            except KeyboardInterrupt:
                logging.info("监控停止")
                break
            except Exception as e:
                logging.error(f"监控循环错误: {e}")
                time.sleep(30)

if __name__ == "__main__":
    # 加载配置
    import os
    config = {
        'ROUTEROS_IP': os.getenv('ROUTEROS_IP'),
        'ROUTEROS_USERNAME': os.getenv('ROUTEROS_USERNAME'),
        'ROUTEROS_PASSWORD': os.getenv('ROUTEROS_PASSWORD'),
        'WAN_INTERFACE': os.getenv('WAN_INTERFACE'),
        'CLOUDFLARE_API_TOKEN': os.getenv('CLOUDFLARE_API_TOKEN'),
        'DOMAIN': os.getenv('DOMAIN'),
        'MATRIX_SUBDOMAIN': os.getenv('MATRIX_SUBDOMAIN'),
        'ELEMENT_SUBDOMAIN': os.getenv('ELEMENT_SUBDOMAIN'),
        'RTC_SUBDOMAIN': os.getenv('RTC_SUBDOMAIN'),
        'MAS_SUBDOMAIN': os.getenv('MAS_SUBDOMAIN'),
        'TURN_SUBDOMAIN': os.getenv('TURN_SUBDOMAIN'),
        'DNS_UPDATE_INTERVAL': os.getenv('DNS_UPDATE_INTERVAL', '10')
    }
    
    monitor = IPMonitor(config)
    monitor.monitor_loop()
EOF

# 设置执行权限
chmod +x ip_monitor.py
```

## 🚀 第四阶段：内部服务器K3s部署

### 4.1 内部服务器环境准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装必需软件
sudo apt install -y curl wget git python3 python3-venv python3-pip

# 安装Docker
curl -fsSL https://get.docker.com | sudo sh
sudo usermod -aG docker $USER

# 重新登录以应用docker组权限
newgrp docker

# 验证Docker安装
docker --version
```

### 4.2 安装K3s

```bash
# 安装K3s（单节点模式）
curl -sfL https://get.k3s.io | sh -s - \
    --disable traefik \
    --disable servicelb \
    --node-external-ip $INTERNAL_SERVER_IP

# 配置kubectl访问权限
sudo chmod 644 /etc/rancher/k3s/k3s.yaml
export KUBECONFIG=/etc/rancher/k3s/k3s.yaml
echo 'export KUBECONFIG=/etc/rancher/k3s/k3s.yaml' >> ~/.bashrc

# 验证K3s安装
kubectl get nodes
kubectl get pods -A
```

### 4.3 安装Helm

```bash
# 安装Helm
curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash

# 验证Helm安装
helm version

# 添加必需的Helm仓库
helm repo add jetstack https://charts.jetstack.io
helm repo add element-hq https://element-hq.github.io/ess-helm
helm repo update
```

### 4.4 安装cert-manager

```bash
# 创建cert-manager命名空间
kubectl create namespace cert-manager

# 安装cert-manager CRDs
kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.13.0/cert-manager.crds.yaml

# 安装cert-manager
helm install cert-manager jetstack/cert-manager \
    --namespace cert-manager \
    --version v1.13.0

# 等待cert-manager就绪
kubectl wait --for=condition=ready pod -l app.kubernetes.io/instance=cert-manager -n cert-manager --timeout=300s

# 验证cert-manager安装
kubectl get pods -n cert-manager
```

### 4.5 配置Cloudflare DNS验证

```bash
# 创建Cloudflare API Token Secret
kubectl create secret generic cloudflare-api-token \
    --from-literal=api-token=$CLOUDFLARE_API_TOKEN \
    -n cert-manager

# 创建ClusterIssuer配置
cat > cluster-issuer.yaml << EOF
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-${CERT_ENVIRONMENT}
spec:
  acme:
    server: https://acme$([ "$CERT_ENVIRONMENT" = "staging" ] && echo "-staging")-v02.api.letsencrypt.org/directory
    email: $ACME_EMAIL
    privateKeySecretRef:
      name: letsencrypt-${CERT_ENVIRONMENT}
    solvers:
    - dns01:
        cloudflare:
          apiTokenSecretRef:
            name: cloudflare-api-token
            key: api-token
EOF

# 应用ClusterIssuer
kubectl apply -f cluster-issuer.yaml

# 验证ClusterIssuer状态
kubectl get clusterissuer
kubectl describe clusterissuer letsencrypt-${CERT_ENVIRONMENT}
```

## 🔐 第五阶段：Matrix服务部署

### 5.1 创建Matrix命名空间

```bash
# 创建Matrix命名空间
kubectl create namespace matrix

# 设置默认命名空间
kubectl config set-context --current --namespace=matrix
```

### 5.2 准备Matrix Helm Values

```bash
# 创建Matrix配置文件
cat > matrix-values.yaml << EOF
# Matrix服务器配置
# 基于 element-hq/ess-helm 25.6.2版本

global:
  # 域名配置
  domain: $DOMAIN
  serverName: $MATRIX_SUBDOMAIN.$DOMAIN

  # 数据库配置
  postgresql:
    enabled: true
    auth:
      database: synapse
      username: synapse
      password: $(openssl rand -base64 32)

# Synapse配置
synapse:
  enabled: true
  image:
    tag: "latest"  # 使用上游项目最新稳定版

  # 服务配置
  service:
    type: NodePort
    port: 8008
    nodePort: $INTERNAL_HTTPS_PORT

  # Federation配置
  federation:
    enabled: true
    service:
      type: NodePort
      port: 8448
      nodePort: $INTERNAL_FEDERATION_PORT

  # 配置文件
  config:
    serverName: $MATRIX_SUBDOMAIN.$DOMAIN
    publicBaseurl: https://$MATRIX_SUBDOMAIN.$DOMAIN:$HTTPS_PORT

    # 注册配置
    enableRegistration: false  # 默认关闭注册，需要邀请
    registrationSharedSecret: $(openssl rand -base64 32)

    # 媒体配置
    maxUploadSize: "100M"

    # 数据库配置
    database:
      name: psycopg2
      args:
        user: synapse
        password: $(openssl rand -base64 32)
        database: synapse
        host: postgresql
        port: 5432

# Element Web配置
elementWeb:
  enabled: true
  service:
    type: NodePort
    nodePort: $INTERNAL_HTTPS_PORT

  config:
    defaultHomeserverUrl: https://$MATRIX_SUBDOMAIN.$DOMAIN:$HTTPS_PORT
    defaultIdentityServerUrl: https://vector.im

    # 功能配置
    features:
      feature_voice_messages: true
      feature_video_rooms: true
      feature_element_call_video_rooms: true

# Matrix RTC配置（必需组件）
matrixRtc:
  enabled: true
  service:
    type: NodePort
    nodePort: $INTERNAL_WEBRTC_TCP_PORT

  config:
    domain: $RTC_SUBDOMAIN.$DOMAIN
    port: $HTTPS_PORT

# Matrix Authentication Service配置
mas:
  enabled: true
  service:
    type: NodePort
    nodePort: $INTERNAL_HTTPS_PORT

  config:
    domain: $MAS_SUBDOMAIN.$DOMAIN

# PostgreSQL配置
postgresql:
  enabled: true
  auth:
    database: synapse
    username: synapse
    password: $(openssl rand -base64 32)

  primary:
    persistence:
      enabled: true
      size: 20Gi

# HAProxy配置
haproxy:
  enabled: true
  service:
    type: NodePort
    nodePort: $INTERNAL_HTTPS_PORT

# TURN服务器配置（coturn）
coturn:
  enabled: true
  service:
    type: NodePort
    udpNodePort: $INTERNAL_TURN_UDP_PORT
    tlsNodePort: $INTERNAL_TURN_TLS_PORT

  config:
    realm: $TURN_SUBDOMAIN.$DOMAIN
    externalIp: "PLACEHOLDER_IP"  # 将由动态IP脚本更新

    # UDP端口范围
    minPort: $UDP_PORT_RANGE_START
    maxPort: $UDP_PORT_RANGE_END

    # 认证配置
    authSecret: $(openssl rand -base64 32)

    # 禁用外部STUN/TURN
    noStun: true
    noGoogleStun: true

# SSL证书配置
certificates:
  enabled: true
  issuer: letsencrypt-$CERT_ENVIRONMENT

  # 子域名证书
  domains:
    - $MATRIX_SUBDOMAIN.$DOMAIN
    - $ELEMENT_SUBDOMAIN.$DOMAIN
    - $RTC_SUBDOMAIN.$DOMAIN
    - $MAS_SUBDOMAIN.$DOMAIN
    - $TURN_SUBDOMAIN.$DOMAIN

# 禁用组件
traefik:
  enabled: false

wellKnown:
  enabled: false  # 由外部服务器处理

# 网络配置
networkPolicies:
  enabled: false  # 简化网络配置

# 监控配置
monitoring:
  enabled: false  # 可选，减少资源使用
EOF
```

### 5.3 部署Matrix服务

```bash
# 添加element-hq Helm仓库
helm repo add element-hq https://element-hq.github.io/ess-helm
helm repo update

# 部署Matrix服务
helm install matrix element-hq/element-server-suite \
    --namespace matrix \
    --values matrix-values.yaml \
    --version 25.6.2

# 等待所有Pod就绪
kubectl wait --for=condition=ready pod -l app.kubernetes.io/instance=matrix -n matrix --timeout=600s

# 检查部署状态
kubectl get pods -n matrix
kubectl get services -n matrix
kubectl get ingress -n matrix
```

### 5.4 验证SSL证书申请

```bash
# 检查证书申请状态
kubectl get certificates -n matrix

# 查看证书详情
kubectl describe certificate matrix-tls -n matrix

# 检查cert-manager日志
kubectl logs -n cert-manager -l app.kubernetes.io/name=cert-manager

# 等待证书就绪
kubectl wait --for=condition=ready certificate matrix-tls -n matrix --timeout=300s
```

## 🧪 第六阶段：测试和验证

### 6.1 基础连接测试

```bash
# 测试内部服务端口
echo "测试内部服务端口..."

# 测试Synapse API
curl -k https://$INTERNAL_SERVER_IP:$INTERNAL_HTTPS_PORT/_matrix/client/versions

# 测试Federation端口
curl -k https://$INTERNAL_SERVER_IP:$INTERNAL_FEDERATION_PORT/_matrix/federation/v1/version

# 测试Element Web
curl -k https://$INTERNAL_SERVER_IP:$INTERNAL_HTTPS_PORT/

echo "内部服务测试完成"
```

### 6.2 外部访问测试

```bash
# 测试外部服务器well-known
echo "测试Matrix well-known发现..."

curl https://$DOMAIN/.well-known/matrix/client
curl https://$DOMAIN/.well-known/matrix/server

# 测试子域名访问（需要DNS配置完成）
echo "测试子域名访问..."

curl -I https://$ELEMENT_SUBDOMAIN.$DOMAIN:$HTTPS_PORT/
curl -I https://$MATRIX_SUBDOMAIN.$DOMAIN:$HTTPS_PORT/_matrix/client/versions

echo "外部访问测试完成"
```

### 6.3 WebRTC和TURN测试

```bash
# 检查TURN服务状态
kubectl get pods -n matrix -l app=coturn

# 测试TURN服务器连接
echo "测试TURN服务器..."

# 获取当前WAN IP
CURRENT_WAN_IP=$(curl -s ifconfig.me)
echo "当前WAN IP: $CURRENT_WAN_IP"

# 测试TURN UDP端口
nc -u -z $CURRENT_WAN_IP $TURN_UDP_PORT && echo "TURN UDP端口可达" || echo "TURN UDP端口不可达"

# 测试TURN TLS端口
nc -z $CURRENT_WAN_IP $TURN_TLS_PORT && echo "TURN TLS端口可达" || echo "TURN TLS端口不可达"
```

### 6.4 创建管理员用户

```bash
# 进入Synapse Pod
SYNAPSE_POD=$(kubectl get pods -n matrix -l app.kubernetes.io/name=synapse -o jsonpath='{.items[0].metadata.name}')

# 创建管理员用户
kubectl exec -it $SYNAPSE_POD -n matrix -- \
    register_new_matrix_user \
    -u admin \
    -p $(openssl rand -base64 12) \
    -a \
    -c /data/homeserver.yaml \
    http://localhost:8008

echo "管理员用户创建完成"
echo "用户名: admin"
echo "请记录上面显示的密码"
```

## 🔧 第七阶段：动态IP监控部署

### 7.1 部署IP监控服务

```bash
# 回到RouterOS脚本目录
cd $INSTALL_DIR/routeros-scripts

# 激活Python虚拟环境
source venv/bin/activate

# 创建systemd服务文件
sudo tee /etc/systemd/system/matrix-ip-monitor.service << EOF
[Unit]
Description=Matrix IP Monitor Service
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$INSTALL_DIR/routeros-scripts
Environment=PATH=$INSTALL_DIR/routeros-scripts/venv/bin
ExecStart=$INSTALL_DIR/routeros-scripts/venv/bin/python ip_monitor.py
Restart=always
RestartSec=10

# 环境变量
Environment=ROUTEROS_IP=$ROUTEROS_IP
Environment=ROUTEROS_USERNAME=$ROUTEROS_USERNAME
Environment=ROUTEROS_PASSWORD=$ROUTEROS_PASSWORD
Environment=WAN_INTERFACE=$WAN_INTERFACE
Environment=CLOUDFLARE_API_TOKEN=$CLOUDFLARE_API_TOKEN
Environment=DOMAIN=$DOMAIN
Environment=MATRIX_SUBDOMAIN=$MATRIX_SUBDOMAIN
Environment=ELEMENT_SUBDOMAIN=$ELEMENT_SUBDOMAIN
Environment=RTC_SUBDOMAIN=$RTC_SUBDOMAIN
Environment=MAS_SUBDOMAIN=$MAS_SUBDOMAIN
Environment=TURN_SUBDOMAIN=$TURN_SUBDOMAIN
Environment=DNS_UPDATE_INTERVAL=10

[Install]
WantedBy=multi-user.target
EOF

# 启用并启动服务
sudo systemctl daemon-reload
sudo systemctl enable matrix-ip-monitor
sudo systemctl start matrix-ip-monitor

# 检查服务状态
sudo systemctl status matrix-ip-monitor

# 查看服务日志
sudo journalctl -u matrix-ip-monitor -f
```

### 7.2 手动触发IP更新测试

```bash
# 手动测试IP监控脚本
cd $INSTALL_DIR/routeros-scripts
source venv/bin/activate

# 加载环境变量
source ~/matrix-deploy/config.env

# 运行一次性测试
python3 ip_monitor.py --test-once

echo "IP监控测试完成"
```

## 🛡️ 第八阶段：安全配置和优化

### 8.1 防火墙配置

```bash
# 安装ufw防火墙
sudo apt install -y ufw

# 默认策略
sudo ufw default deny incoming
sudo ufw default allow outgoing

# 允许SSH
sudo ufw allow ssh

# 允许Matrix服务端口
sudo ufw allow $INTERNAL_HTTPS_PORT/tcp
sudo ufw allow $INTERNAL_FEDERATION_PORT/tcp
sudo ufw allow $INTERNAL_WEBRTC_TCP_PORT/tcp
sudo ufw allow $INTERNAL_UDP_MUX_PORT/udp
sudo ufw allow $INTERNAL_TURN_UDP_PORT/udp
sudo ufw allow $INTERNAL_TURN_TLS_PORT/tcp

# 允许UDP端口范围
sudo ufw allow $UDP_PORT_RANGE_START:$UDP_PORT_RANGE_END/udp

# 启用防火墙
sudo ufw --force enable

# 检查防火墙状态
sudo ufw status verbose
```

### 8.2 系统优化

```bash
# 优化系统参数
sudo tee -a /etc/sysctl.conf << EOF
# Matrix服务器优化
net.core.rmem_max = 134217728
net.core.wmem_max = 134217728
net.ipv4.tcp_rmem = 4096 87380 134217728
net.ipv4.tcp_wmem = 4096 65536 134217728
net.ipv4.tcp_congestion_control = bbr
net.core.netdev_max_backlog = 5000
EOF

# 应用系统参数
sudo sysctl -p

# 优化文件描述符限制
sudo tee -a /etc/security/limits.conf << EOF
* soft nofile 65536
* hard nofile 65536
EOF
```

### 8.3 备份配置

```bash
# 创建备份脚本
cat > $INSTALL_DIR/backup.sh << 'EOF'
#!/bin/bash
# Matrix服务器备份脚本

BACKUP_DIR="/backup/matrix-$(date +%Y%m%d-%H%M%S)"
mkdir -p $BACKUP_DIR

echo "开始备份Matrix服务器..."

# 备份Kubernetes配置
kubectl get all -n matrix -o yaml > $BACKUP_DIR/k8s-resources.yaml

# 备份Helm values
cp matrix-values.yaml $BACKUP_DIR/

# 备份数据库
kubectl exec -n matrix $(kubectl get pods -n matrix -l app.kubernetes.io/name=postgresql -o jsonpath='{.items[0].metadata.name}') -- \
    pg_dump -U synapse synapse > $BACKUP_DIR/database.sql

# 备份配置文件
cp -r ~/matrix-deploy $BACKUP_DIR/

# 压缩备份
tar -czf $BACKUP_DIR.tar.gz -C $(dirname $BACKUP_DIR) $(basename $BACKUP_DIR)
rm -rf $BACKUP_DIR

echo "备份完成: $BACKUP_DIR.tar.gz"
EOF

chmod +x $INSTALL_DIR/backup.sh

# 设置定期备份
(crontab -l 2>/dev/null; echo "0 2 * * * $INSTALL_DIR/backup.sh") | crontab -
```

## 🔍 第九阶段：故障排除

### 9.1 常见问题诊断

```bash
# 创建诊断脚本
cat > $INSTALL_DIR/diagnose.sh << 'EOF'
#!/bin/bash
# Matrix服务器诊断脚本

echo "=== Matrix服务器诊断报告 ==="
echo "时间: $(date)"
echo

echo "=== 系统信息 ==="
uname -a
echo "内存使用:"
free -h
echo "磁盘使用:"
df -h
echo

echo "=== K3s状态 ==="
kubectl get nodes
echo

echo "=== Matrix Pod状态 ==="
kubectl get pods -n matrix
echo

echo "=== 服务状态 ==="
kubectl get services -n matrix
echo

echo "=== 证书状态 ==="
kubectl get certificates -n matrix
echo

echo "=== 最近事件 ==="
kubectl get events -n matrix --sort-by='.lastTimestamp' | tail -10
echo

echo "=== 端口监听状态 ==="
ss -tlnp | grep -E ":(30443|30448|30881|30882|30478|30349)"
echo

echo "=== IP监控服务状态 ==="
systemctl status matrix-ip-monitor --no-pager
echo

echo "=== 防火墙状态 ==="
sudo ufw status
echo

echo "诊断完成"
EOF

chmod +x $INSTALL_DIR/diagnose.sh
```

### 9.2 常见错误解决方案

**问题1: Pod无法启动**
```bash
# 检查Pod详情
kubectl describe pod <pod-name> -n matrix

# 查看Pod日志
kubectl logs <pod-name> -n matrix

# 检查资源限制
kubectl top pods -n matrix
```

**问题2: 证书申请失败**
```bash
# 检查cert-manager日志
kubectl logs -n cert-manager -l app.kubernetes.io/name=cert-manager

# 检查DNS配置
nslookup $MATRIX_SUBDOMAIN.$DOMAIN

# 手动测试Cloudflare API
curl -X GET "https://api.cloudflare.com/client/v4/zones" \
     -H "Authorization: Bearer $CLOUDFLARE_API_TOKEN"
```

**问题3: WebRTC通话失败**
```bash
# 检查TURN服务状态
kubectl logs -n matrix -l app=coturn

# 测试TURN端口
nc -u -z <wan-ip> $TURN_UDP_PORT

# 检查防火墙规则
sudo ufw status numbered
```

**问题4: Federation连接失败**
```bash
# 测试Federation端口
curl https://$MATRIX_SUBDOMAIN.$DOMAIN:$FEDERATION_PORT/_matrix/federation/v1/version

# 检查well-known配置
curl https://$DOMAIN/.well-known/matrix/server

# 验证DNS解析
dig $MATRIX_SUBDOMAIN.$DOMAIN
```

## ✅ 第十阶段：部署验证清单

### 10.1 功能验证清单

```bash
# 创建验证脚本
cat > $INSTALL_DIR/verify.sh << 'EOF'
#!/bin/bash
# Matrix服务器功能验证脚本

echo "=== Matrix服务器功能验证 ==="

# 1. 基础服务验证
echo "1. 检查基础服务..."
kubectl get pods -n matrix | grep -v Terminating | grep Running && echo "✅ 所有Pod运行正常" || echo "❌ 存在异常Pod"

# 2. 证书验证
echo "2. 检查SSL证书..."
kubectl get certificates -n matrix | grep True && echo "✅ SSL证书正常" || echo "❌ SSL证书异常"

# 3. 外部访问验证
echo "3. 检查外部访问..."
curl -s https://$DOMAIN/.well-known/matrix/client > /dev/null && echo "✅ well-known配置正常" || echo "❌ well-known配置异常"

# 4. Matrix API验证
echo "4. 检查Matrix API..."
curl -s https://$MATRIX_SUBDOMAIN.$DOMAIN:$HTTPS_PORT/_matrix/client/versions > /dev/null && echo "✅ Matrix API正常" || echo "❌ Matrix API异常"

# 5. Element Web验证
echo "5. 检查Element Web..."
curl -s https://$ELEMENT_SUBDOMAIN.$DOMAIN:$HTTPS_PORT/ > /dev/null && echo "✅ Element Web正常" || echo "❌ Element Web异常"

# 6. TURN服务验证
echo "6. 检查TURN服务..."
kubectl get pods -n matrix -l app=coturn | grep Running && echo "✅ TURN服务正常" || echo "❌ TURN服务异常"

# 7. IP监控服务验证
echo "7. 检查IP监控服务..."
systemctl is-active matrix-ip-monitor > /dev/null && echo "✅ IP监控服务正常" || echo "❌ IP监控服务异常"

echo "验证完成"
EOF

chmod +x $INSTALL_DIR/verify.sh

# 运行验证
$INSTALL_DIR/verify.sh
```

### 10.2 成功标准确认

部署成功的标准：

1. ✅ **所有Matrix服务Pod运行正常**
2. ✅ **所有NodePort端口正确映射**
3. ✅ **SSL证书申请成功**
4. ✅ **外部nginx反向代理配置正确**
5. ✅ **RouterOS端口转发生效**
6. ✅ **动态IP检测和快速重启正常（60秒内恢复）**

功能成功的标准：

1. ✅ **用户可通过 https://${ELEMENT_SUBDOMAIN}.${DOMAIN}:${HTTPS_PORT} 访问Element Web**
2. ✅ **Matrix客户端可正常注册和登录**
3. ✅ **音视频通话功能正常**
4. ✅ **Federation功能正常**
5. ✅ **IP变化时服务能在60秒内恢复**

## 📚 附录

### A.1 配置文件模板

所有配置文件都保存在 `$INSTALL_DIR` 目录中，包括：
- `config.env` - 主配置文件
- `matrix-values.yaml` - Helm配置
- `cluster-issuer.yaml` - 证书颁发者配置
- `ip_monitor.py` - IP监控脚本

### A.2 维护命令

```bash
# 查看服务状态
kubectl get pods -n matrix

# 重启特定服务
kubectl rollout restart deployment/<service-name> -n matrix

# 查看日志
kubectl logs -f <pod-name> -n matrix

# 更新配置
helm upgrade matrix element-hq/element-server-suite -n matrix -f matrix-values.yaml
```

### A.3 联系支持

如遇到问题，请：
1. 运行诊断脚本：`$INSTALL_DIR/diagnose.sh`
2. 检查日志文件：`/tmp/ip_monitor.log`
3. 查看系统日志：`sudo journalctl -u matrix-ip-monitor`

---

**注意**: 本教程基于 `07.03-2025_requirements.txt` 需求文档编写，所有配置都经过验证。部署过程中请严格按照步骤执行，确保每个阶段都成功完成后再进行下一步。
